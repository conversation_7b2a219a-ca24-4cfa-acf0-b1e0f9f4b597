package cmd

import (
	"os"
	"os/signal"
	"syscall"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"golang.org/x/exp/slog"
)

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run",
	Short: "Runs this application i commandline mode",
	Long:  `Runs this application i commandline mode for debugging purposes`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get Visma configuration from viper
		vismaServer := viper.GetString("visma.server")
		vismaInstance := viper.GetString("visma.instance")
		vismaPort := viper.GetInt("visma.port")
		vismaUsername := viper.GetString("visma.username")
		vismaPassword := viper.GetString("visma.password")
		vismaDatabase := viper.GetString("visma.database")
		vismaParams := viper.GetStringMapString("visma.params")

		// Log Visma configuration
		slog.Debug("Visma configuration", "server", vismaServer, "instance",
			vismaInstance, "port", vismaPort, "username", vismaUsername, "password",
			vismaPassword, "database", vismaDatabase, "params", vismaParams)

		client, err := vismadb.NewClient(&vismadb.ClientConfig{
			Server:   vismaServer,
			Instance: vismaInstance,
			Port:     vismaPort,
			Username: vismaUsername,
			Password: vismaPassword,
			Database: vismaDatabase,
			Params:   vismaParams,
		})
		if err != nil {
			slog.Error("error creating visma client", "error", err)
			os.Exit(1)
		}
		defer client.Close()

		viper.Set("visma.client", client)

		go func() {
			if err := httpserver.Start(); err != nil {
				slog.Error("error starting http server", "error", err)
				os.Exit(1)
			}
		}()
		slog.Info("HTTP-server started", "port", viper.GetInt("http.port"))

		// This will catch CTRL+C, do cleanup and exit
		c := make(chan os.Signal, 2)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM, syscall.SIGKILL, syscall.SIGQUIT)

		//Wait until we get the quit message
		<-c
		slog.Info("Quitting...")
	},
}

func init() {
	rootCmd.AddCommand(runCmd)

	/* -- HTTP-server flags -- */
	runCmd.PersistentFlags().String("http-listen", "0.0.0.0", "The address to listen on for HTTP-traffic")
	viper.BindPFlag("http.listen", runCmd.PersistentFlags().Lookup("http-listen"))
	runCmd.PersistentFlags().Int("http-port", 8080, "The port to listen on for HTTP-traffic")
	viper.BindPFlag("http.port", runCmd.PersistentFlags().Lookup("http-port"))
	/* -- HTTP-server username and password -- */
	runCmd.PersistentFlags().String("http-username", "admin", "The username to use for HTTP-traffic")
	viper.BindPFlag("http.username", runCmd.PersistentFlags().Lookup("http-username"))
	runCmd.PersistentFlags().String("http-password", "admin", "The password to use for HTTP-traffic")
	viper.BindPFlag("http.password", runCmd.PersistentFlags().Lookup("http-password"))

	/* -- Visma configuration -- */
	runCmd.PersistentFlags().String("visma-server", "", "The server to use for Visma")
	viper.BindPFlag("visma.server", runCmd.PersistentFlags().Lookup("visma-server"))
	runCmd.PersistentFlags().String("visma-instance", "", "The instance to use for Visma")
	viper.BindPFlag("visma.instance", runCmd.PersistentFlags().Lookup("visma-instance"))
	runCmd.PersistentFlags().String("visma-username", "", "The username to use for Visma")
	viper.BindPFlag("visma.username", runCmd.PersistentFlags().Lookup("visma-username"))
	runCmd.PersistentFlags().String("visma-password", "", "The password to use for Visma")
	viper.BindPFlag("visma.password", runCmd.PersistentFlags().Lookup("visma-password"))
	runCmd.PersistentFlags().String("visma-companyname", "ERV Teknikk AS", "The company name to use for Visma")
	viper.BindPFlag("visma.companyname", runCmd.PersistentFlags().Lookup("visma-companyname"))
	runCmd.PersistentFlags().String("visma-database", "F0001", "The database to use for Visma")
	viper.BindPFlag("visma.database", runCmd.PersistentFlags().Lookup("visma-database"))
	runCmd.PersistentFlags().Int("visma-port", 1433, "The port to use for Visma")
	viper.BindPFlag("visma.port", runCmd.PersistentFlags().Lookup("visma-port"))

	/* -- Export configuration -- */
	runCmd.PersistentFlags().String("export-layout", "VLSTANDARD", "The layout to use for export")
	viper.BindPFlag("export.layout", runCmd.PersistentFlags().Lookup("export-layout"))
	runCmd.PersistentFlags().String("export-file", viper.GetString("appDir")+"\\go-lonn-exporter.csv", "The file to save export to")
	viper.BindPFlag("export.file", runCmd.PersistentFlags().Lookup("export-file"))
	runCmd.PersistentFlags().Bool("export-overwrite", false, "Overwrite the export file if it exists")
	viper.BindPFlag("export.overwrite", runCmd.PersistentFlags().Lookup("export-overwrite"))
}
