package httpserver

import (
	"fmt"
	"net/http"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/handlers"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

func use(h http.HandlerFunc, middleware ...func(http.HandlerFunc) http.HandlerFunc) http.HandlerFunc {
	for _, m := range middleware {
		h = m(h)
	}
	return h
}

// Start starts a http-server on the specified port
func Start() error {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		return fmt.Errorf("failed to get embedded assets: %w", err)
	}

	// Use embedded filesystem for static files
	fs := http.FileServer(http.FS(staticFS))
	http.Handle("/public/", http.StripPrefix("/public/", fs))

	// Determine which auth middleware to use based on configuration
	var authMiddleware func(http.HandlerFunc) http.HandlerFunc
	if viper.GetBool("keycloak.enabled") {
		authMiddleware = middleware.KeycloakMiddleware
	} else {
		authMiddleware = handlers.BasicAuth
	}

	http.HandleFunc("/", use(handlers.ServeTemplates, authMiddleware))
	http.HandleFunc("/employees", use(handlers.Employees, authMiddleware))
	http.HandleFunc("/export", use(handlers.Export, authMiddleware))
	http.HandleFunc("/download", use(handlers.Download, authMiddleware))
	http.HandleFunc("/report", use(handlers.Report, authMiddleware))

	return http.ListenAndServe(fmt.Sprintf(":%d", viper.GetInt("http.port")), nil)
}
