package httpserver

import (
	"fmt"
	"net/http"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/handlers"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/public"
)

// Config holds the configuration for the HTTP server
type Config struct {
	Port     int
	Username string
	Password string
	Keycloak *KeycloakConfig
}

// KeycloakConfig holds the Keycloak authentication configuration
type KeycloakConfig struct {
	Enabled      bool
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
}

func use(h http.HandlerFunc, middleware ...func(http.HandlerFunc) http.HandlerFunc) http.HandlerFunc {
	for _, m := range middleware {
		h = m(h)
	}
	return h
}

// Start starts a http-server with the provided configuration
func Start(config *Config) error {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		return fmt.Errorf("failed to get embedded assets: %w", err)
	}

	// Use embedded filesystem for static files
	fs := http.FileServer(http.FS(staticFS))
	http.Handle("/public/", http.StripPrefix("/public/", fs))

	// Determine which auth middleware to use based on configuration
	var authMiddleware func(http.HandlerFunc) http.HandlerFunc
	if config.Keycloak != nil && config.Keycloak.Enabled {
		// Convert to middleware.KeycloakConfig
		keycloakConfig := &middleware.KeycloakConfig{
			URL:          config.Keycloak.URL,
			Realm:        config.Keycloak.Realm,
			ClientID:     config.Keycloak.ClientID,
			ClientSecret: config.Keycloak.ClientSecret,
			RequiredRole: config.Keycloak.RequiredRole,
		}
		authMiddleware = middleware.NewKeycloakMiddleware(keycloakConfig)
	} else {
		authMiddleware = handlers.BasicAuth
	}

	http.HandleFunc("/", use(handlers.ServeTemplates, authMiddleware))
	http.HandleFunc("/employees", use(handlers.Employees, authMiddleware))
	http.HandleFunc("/export", use(handlers.Export, authMiddleware))
	http.HandleFunc("/download", use(handlers.Download, authMiddleware))
	http.HandleFunc("/report", use(handlers.Report, authMiddleware))

	return http.ListenAndServe(fmt.Sprintf(":%d", config.Port), nil)
}
