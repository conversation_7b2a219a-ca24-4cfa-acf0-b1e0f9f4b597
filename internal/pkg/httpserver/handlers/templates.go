package handlers

import (
	"html/template"
	"io/fs"
	"log/slog"
	"net/http"
	"os"
	"path"

	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

// ServeTemplates ... kind of just serves /
func ServeTemplates(w http.ResponseWriter, r *http.Request) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Clean and prepare paths
	cleanPath := path.Clean(r.URL.Path)
	if cleanPath == "/" {
		cleanPath = "/index.html"
	}

	// Construct template paths
	layoutPath := "templates/layout.html"
	templatePath := "templates" + cleanPath

	// Check if template exists
	if _, err := fs.Stat(staticFS, templatePath); err != nil {
		if os.IsNotExist(err) {
			http.NotFound(w, r)
			return
		}
		slog.Error("error checking template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Parse templates from embedded filesystem
	tmpl, err := template.ParseFS(staticFS, layoutPath, templatePath)
	if err != nil {
		slog.Error("error parsing template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	data := struct {
		Version     string
		CompanyName string
		Database    string
	}{
		viper.GetString("version"),
		viper.GetString("visma.companyname"),
		viper.GetString("visma.database"),
	}

	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}
