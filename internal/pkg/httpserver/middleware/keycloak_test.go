// filepath: /Users/<USER>/Developer/github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware/keycloak_test.go
package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestKeycloakMiddleware_NoAuthHeader(t *testing.T) {
	// Mock configuration
	viper.Set("keycloak.url", "https://keycloak.example.com/auth")
	viper.Set("keycloak.realm", "testrealm")
	viper.Set("keycloak.client_id", "testclient")
	viper.Set("keycloak.client_secret", "testsecret")
	viper.Set("keycloak.required_role", "lonnexport")

	// Create a handler that should not be called
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Test with no Authorization header
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	middleware := KeycloakMiddleware(handler)
	middleware.ServeHTTP(rr, req)

	// Should return 401 when no Authorization header
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Authorization header is required")
}

func TestKeycloakMiddleware_InvalidAuthHeader(t *testing.T) {
	// Mock configuration
	viper.Set("keycloak.url", "https://keycloak.example.com/auth")
	viper.Set("keycloak.realm", "testrealm")
	viper.Set("keycloak.client_id", "testclient")
	viper.Set("keycloak.client_secret", "testsecret")
	viper.Set("keycloak.required_role", "lonnexport")

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Test with invalid Authorization header format
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "InvalidFormat")
	rr := httptest.NewRecorder()

	middleware := KeycloakMiddleware(handler)
	middleware.ServeHTTP(rr, req)

	// Should return 401 when format is invalid
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Authorization header format must be Bearer")
}

func TestKeycloakMiddleware_InvalidToken(t *testing.T) {
	// Mock configuration
	viper.Set("keycloak.url", "https://keycloak.example.com/auth")
	viper.Set("keycloak.realm", "testrealm")
	viper.Set("keycloak.client_id", "testclient")
	viper.Set("keycloak.client_secret", "testsecret")
	viper.Set("keycloak.required_role", "lonnexport")

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Test with bearer but invalid token
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer invalid_token")
	rr := httptest.NewRecorder()

	middleware := KeycloakMiddleware(handler)
	middleware.ServeHTTP(rr, req)

	// Should return 401 with invalid token
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Invalid token")
}

func TestHasAccess(t *testing.T) {
	tests := []struct {
		name         string
		claims       jwt.MapClaims
		requiredRole string
		expected     bool
	}{
		{
			name: "realm role match",
			claims: jwt.MapClaims{
				"realm_access": map[string]interface{}{
					"roles": []interface{}{"lonnexport", "other-role"},
				},
			},
			requiredRole: "lonnexport",
			expected:     true,
		},
		{
			name: "client role match",
			claims: jwt.MapClaims{
				"resource_access": map[string]interface{}{
					"testclient": map[string]interface{}{
						"roles": []interface{}{"lonnexport"},
					},
				},
			},
			requiredRole: "lonnexport",
			expected:     true,
		},
		{
			name: "group match",
			claims: jwt.MapClaims{
				"groups": []interface{}{"/lonnexport", "/other-group"},
			},
			requiredRole: "lonnexport",
			expected:     true,
		},
		{
			name: "no match",
			claims: jwt.MapClaims{
				"realm_access": map[string]interface{}{
					"roles": []interface{}{"other-role"},
				},
			},
			requiredRole: "lonnexport",
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := hasAccess(tt.claims, tt.requiredRole)
			require.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}
