// filepath: /Users/<USER>/Developer/github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware/keycloak_test.go
package middleware

import (
"net/http"
"net/http/httptest"
"testing"

"github.com/spf13/viper"
"github.com/stretchr/testify/assert"
)

func TestKeycloakMiddleware(t *testing.T) {
	// Mock configuration
	viper.Set("keycloak.url", "https://keycloak.example.com/auth")
	viper.Set("keycloak.realm", "testrealm")
	viper.Set("keycloak.client_id", "testclient")
	viper.Set("keycloak.client_secret", "testsecret")
	viper.Set("keycloak.required_role", "lonnexport")

	// This test is simply to ensure the function structure works and returns 401 without a token
	// For complete testing, you would need to mock the Keycloak client

	// Create a handler that verifies context data
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
w.WriteHeader(http.StatusOK)
w.Write([]byte("success"))
})

	// Test with no Authorization header
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	middleware := KeycloakMiddleware(handler)
	middleware.ServeHTTP(rr, req)

	// Should return 401 when no Authorization header
	assert.Equal(t, http.StatusUnauthorized, rr.Code)

	// Test with invalid Authorization header
	req = httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "InvalidFormat")
	rr = httptest.NewRecorder()

	middleware.ServeHTTP(rr, req)

	// Should return 401 when format is invalid
	assert.Equal(t, http.StatusUnauthorized, rr.Code)

	// Test with bearer but invalid token
	req = httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer invalid_token")
	rr = httptest.NewRecorder()

	middleware.ServeHTTP(rr, req)

	// Should return 401 with invalid token
	assert.Equal(t, http.StatusUnauthorized, rr.Code)

	// Note: For complete testing, you would need to mock the Keycloak client
	// and test the token verification and role checking
}
