package exporter

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/stretchr/testify/assert"
)

func TestFormatVismaLine(t *testing.T) {
	tests := []struct {
		name     string
		input    vismadb.OrdLn
		expected string
		wantErr  bool
	}{
		{
			name: "standard line with default fields (Comments and Extra disabled)",
			input: vismadb.OrdLn{
				RunNo:   0,
				EmpNo:   113,
				WageSrt: "10100",
				Bas:     0.00,
				Qty:     7.50,
				TrDt:    20250206,
			},
			expected: `0;0;113;10100;0.00;7.50;0.00;0.00;;;"";"";"";"";"";"";;06.02.2025`,
			wantErr:  false,
		},
		{
			name: "line with negative quantity (Comments and Extra disabled)",
			input: vismadb.OrdLn{
				RunNo:   2,
				EmpNo:   198,
				WageSrt: "11161",
				Bas:     0.00,
				Qty:     -0.50,
				TrDt:    20240202,
			},
			expected: `0;2;198;11161;0.00;-0.50;0.00;0.00;;;"";"";"";"";"";"";;02.02.2024`,
			wantErr:  false,
		},
		{
			name: "line with zero values (Comments and Extra disabled)",
			input: vismadb.OrdLn{
				RunNo:   3,
				EmpNo:   198,
				WageSrt: "11161",
				Bas:     0.00,
				Qty:     0.00,
				TrDt:    20240202,
			},
			expected: `0;3;198;11161;0.00;0.00;0.00;0.00;;;"";"";"";"";"";"";;02.02.2024`,
			wantErr:  false,
		},
		{
			name: "line with invalid date (Comments and Extra disabled)",
			input: vismadb.OrdLn{
				RunNo:   4,
				EmpNo:   198,
				WageSrt: "11161",
				Bas:     100.00,
				Qty:     1.00,
				TrDt:    0, // Invalid date
			},
			expected: `0;4;198;11161;100.00;1.00;0.00;0.00;;;"";"";"";"";"";"";;`,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := formatVismaLine(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, got, "Test case: %s", tt.name)
		})
	}
}

func TestExportToVisma(t *testing.T) {
	orderlines := []vismadb.OrdLn{
		{
			RunNo: 0, EmpNo: 113, WageSrt: "10100",
			Bas: 0.00, Qty: 7.50, TrDt: 20250206,
		},
		{
			RunNo: 2, EmpNo: 198, WageSrt: "11161",
			Bas: 0.00, Qty: -0.50, TrDt: 20240202,
		},
	}

	expected := []string{
		`0;0;113;10100;0.00;7.50;0.00;0.00;;;"";"";"";"";"";"";;06.02.2025`,
		`0;2;198;11161;0.00;-0.50;0.00;0.00;;;"";"";"";"";"";"";;02.02.2024`,
	}

	got, err := ExportToVisma(&orderlines)
	assert.NoError(t, err)
	assert.Equal(t, expected, got)
}

func TestExportOrderLinesToFile(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir, err := os.MkdirTemp("", "visma_test")
	assert.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	testFile := filepath.Join(tmpDir, "test_export.txt")

	orderlines := []vismadb.OrdLn{
		{
			RunNo: 0, EmpNo: 113, WageSrt: "10100",
			Bas: 0.00, Qty: 7.50, TrDt: 20250206,
		},
		{
			RunNo: 2, EmpNo: 198, WageSrt: "11161",
			Bas: 0.00, Qty: -0.50, TrDt: 20240202,
		},
	}

	tests := []struct {
		name      string
		config    *VismaExporterConfig
		wantErr   bool
		overwrite bool
	}{
		{
			name: "successful export",
			config: &VismaExporterConfig{
				FileFormat: VLSTANDARD,
				File:       testFile,
				Overwrite:  true,
			},
			wantErr: false,
		},
		{
			name: "file exists and no overwrite",
			config: &VismaExporterConfig{
				FileFormat: VLSTANDARD,
				File:       testFile,
				Overwrite:  false,
			},
			wantErr: true,
		},
		{
			name: "invalid format",
			config: &VismaExporterConfig{
				FileFormat: VismaExportType(999),
				File:       testFile,
				Overwrite:  true,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.ExportOrderLinesToFile(&orderlines)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// Verify file contents if export was successful
			if !tt.wantErr {
				content, err := os.ReadFile(tt.config.File)
				assert.NoError(t, err)
				expected := `0;0;113;10100;0.00;7.50;0.00;0.00;;;"";"";"";"";"";"";;06.02.2025` + "\n" +
					`0;2;198;11161;0.00;-0.50;0.00;0.00;;;"";"";"";"";"";"";;02.02.2024`
				assert.Equal(t, expected, string(content))
			}
		})
	}
}

func TestFormatDate(t *testing.T) {
	tests := []struct {
		name     string
		input    int
		expected string
	}{
		{
			name:     "valid date",
			input:    20250206,
			expected: "06.02.2025",
		},
		{
			name:     "invalid date - zero",
			input:    0,
			expected: "",
		},
		{
			name:     "invalid date - negative",
			input:    -20240202,
			expected: "",
		},
		{
			name:     "invalid date - wrong format",
			input:    240202,
			expected: "",
		},
		{
			name:     "invalid month",
			input:    20241302,
			expected: "",
		},
		{
			name:     "invalid day",
			input:    20240232,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatDate(tt.input)
			assert.Equal(t, tt.expected, got)
		})
	}
}
