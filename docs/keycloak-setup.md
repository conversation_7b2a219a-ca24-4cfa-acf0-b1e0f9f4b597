## Keycloak Authentication Setup Guide

This guide explains how to set up Keycloak authentication for the ERV Go Lonn Exporter.

### Prerequisites

1. A running Keycloak server instance
2. Administrative access to the Keycloak server

### Keycloak Configuration

1. **Create a new Realm** (or use an existing one)
   - Log in to the Keycloak Admin Console
   - Create a new realm (e.g., "lonnexporter-realm") or select an existing one

2. **Create a Client**
   - In your realm, go to "Clients" and click "Create"
   - Set the Client ID (e.g., "lonnexporter")
   - Set Client Protocol to "openid-connect"
   - Set Access Type to "confidential"
   - Set Valid Redirect URIs to match your application URLs (e.g., "http://localhost:8080/*")
   - Enable "Service Accounts Enabled" if your app needs to use the client credentials flow
   - Save the client

3. **Get Client Secret**
   - After creating the client, go to the "Credentials" tab
   - Copy the client secret, which you'll need for your application configuration

4. **Create Roles**
   - Go to "Roles" and click "Add Role"
   - Create a role named "lonnexport"
   - Save the role

5. **Create Users**
   - Go to "Users" and click "Add user"
   - Fill in the required information and save
   - Go to the "Credentials" tab for the user and set a password
   - Go to the "Role Mappings" tab
   - Assign the "lonnexport" role to the user

6. (Optional) **Create a Group**
   - Go to "Groups" and click "New"
   - Create a group named "lonnexport"
   - Add users to this group
   - Map roles to this group if needed

### Application Configuration

1. Update your `conf/app.toml` file with the Keycloak settings:

```toml
# Set to true in production environments
production = true

[keycloak]
# Enable Keycloak authentication
enabled = true

# URL to the Keycloak server
url = "https://your-keycloak-server/auth"

# Realm to use
realm = "your-realm"

# Client ID
client_id = "your-client-id"

# Client secret
client_secret = "your-client-secret"

# Required role or group
required_role = "lonnexport"
```

2. Restart your application for the changes to take effect.

### Development vs. Production Mode

The application supports two different Keycloak middleware implementations:

1. **Development Mode** (`production = false`):
   - Uses a simplified middleware that doesn't make all API calls to Keycloak
   - Useful for local development and testing
   - Less strict token validation

2. **Production Mode** (`production = true`):
   - Uses a full-featured middleware with complete Keycloak integration
   - Makes multiple API calls to verify token, user roles, and groups
   - Required for secure production environments

### Testing Authentication

1. Get a token from Keycloak using one of these methods:
   - Direct login via Keycloak REST API
   - Using the Keycloak JavaScript adapter in a web application
   - Using a token from the Keycloak account console for testing

2. Use the token in requests to your application:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/
   ```

### Troubleshooting

- Check Keycloak server logs for authentication issues
- Verify that the user has the "lonnexport" role or is in the "lonnexport" group
- Ensure that the client secret in your configuration matches the one in Keycloak
- Check that the token is not expired
