# Example configuration-file

# Set to true in production environments
production = false

[http]
# Port the HTTP user-interface runs at, default 8080
port = 8080

# Basic auth for HTTP
username = "admin"
password = "admin"

[keycloak]
# Enable Keycloak authentication, when false, basic auth is used
enabled = false

# URL to the Keycloak server
url = "https://keycloak.example.com/auth"

# Realm to use
realm = "myrealm"

# Client ID
client_id = "lonnexporter"

# Client secret
client_secret = "your-client-secret"

# Required role or group, users must have this role/group to access the application
required_role = "lonnexport"

[visma]
# Server hostname or ip
server = ""

# Port where the database-service runs at (integer)
port = 0

# Instance name to find the database-service port from sqlbrowser
instance = ""

# Username to log in with
username = ""

# Password to log in with
password = ""

# Database to use
database = "F0001"

# Companyname to show for this database
companyname = "ERV Teknikk AS"

# Extra parameters to use for the database-connection
params = [
    { key = "param1", value = "value1" },
    { key = "param2", value = "value2" },
]

[export]
# Layout of the output file, currently supports only "VLSTANDARD"
layout = "VLSTANDARD"

# Path and filename to write the export-data to
file = '\\somepath\somefilename.somextension'

# Overwrite existing file if found
overwrite = true

[logging]
# Log levels info, warning, fatal, debug
level = "info"

# Log format
format = "pretty"

# Logfile to output "error" to (stderr)
output = "stdout"

# Logfile to output "output" to (stdout)
file = "lonnexporter.log"
